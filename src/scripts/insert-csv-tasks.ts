#!/usr/bin/env tsx

/**
 * Quick script to insert print tasks from AI JSON response for CSV bulk orders
 * Usage: tsx src/scripts/insert-csv-tasks.ts --order-id 32242 --json-file path/to/ai-response.json
 */

import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';
import { readFileSync } from 'fs';
import { Command } from 'commander';
import crypto from 'crypto';

// Load environment variables
config();

const prisma = new PrismaClient();

interface PersonalizationDetail {
  customText: string;
  color1: string;
  color2: string | null;
  quantity: number;
  needsReview: boolean;
  reviewReason: string | null;
}

interface ItemPersonalization {
  personalizations: PersonalizationDetail[];
  overallNeedsReview: boolean;
  overallReviewReason: string | null;
}

interface AIResponse {
  itemPersonalizations: Record<string, ItemPersonalization>;
}

async function insertTasksFromJSON(orderId: number, jsonFilePath?: string, jsonData?: string): Promise<void> {
  try {
    console.log(`Starting task insertion for order ${orderId}...`);

    // Parse JSON data
    let aiResponse: AIResponse;
    if (jsonData) {
      aiResponse = JSON.parse(jsonData);
    } else if (jsonFilePath) {
      const jsonContent = readFileSync(jsonFilePath, 'utf-8');
      aiResponse = JSON.parse(jsonContent);
    } else {
      throw new Error('Either --json-file or --json-data must be provided');
    }

    // Get order and order items
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        OrderItem: {
          include: {
            Product: true
          }
        }
      }
    });

    if (!order) {
      throw new Error(`Order ${orderId} not found`);
    }

    console.log(`Found order ${orderId} with ${order.OrderItem.length} items`);

    // Clear existing tasks for this order (since we're recreating)
    const deletedTasks = await prisma.printOrderTask.deleteMany({
      where: { orderId: orderId }
    });
    console.log(`Deleted ${deletedTasks.count} existing tasks`);

    let totalTasksCreated = 0;

    // Process each item
    for (const orderItem of order.OrderItem) {
      const itemKey = orderItem.shipstationLineItemKey || `item-${orderItem.id}`;
      const itemPersonalization = aiResponse.itemPersonalizations[itemKey] || 
                                 aiResponse.itemPersonalizations['item-1']; // Fallback for single item orders

      if (!itemPersonalization) {
        console.warn(`No personalization data found for item ${orderItem.id} (key: ${itemKey})`);
        continue;
      }

      console.log(`Processing item ${orderItem.id} with ${itemPersonalization.personalizations.length} personalizations`);

      // Create tasks for each personalization
      for (let i = 0; i < itemPersonalization.personalizations.length; i++) {
        const personalization = itemPersonalization.personalizations[i];
        
        const taskData = {
          id: crypto.randomUUID(),
          orderId: orderId,
          orderItemId: orderItem.id,
          productId: orderItem.productId,
          taskIndex: i,
          custom_text: personalization.customText,
          color_1: personalization.color1,
          color_2: personalization.color2,
          quantity: personalization.quantity,
          status: 'pending' as const,
          needs_review: personalization.needsReview,
          review_reason: personalization.reviewReason,
          stl_render_state: 'pending' as const,
          shorthandProductName: orderItem.Product?.name || 'Unknown Product',
          marketplace_order_number: order.shipstation_order_number,
          annotation: 'Created from CSV AI response'
        };

        const task = await prisma.printOrderTask.create({
          data: taskData
        });

        totalTasksCreated++;
        
        if (i % 50 === 0 || i === itemPersonalization.personalizations.length - 1) {
          console.log(`Created ${i + 1}/${itemPersonalization.personalizations.length} tasks for item ${orderItem.id}`);
        }
      }
    }

    console.log(`Successfully created ${totalTasksCreated} print tasks for order ${orderId}`);

  } catch (error) {
    console.error('Error inserting tasks:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// CLI setup
const program = new Command();
program
  .name('insert-csv-tasks')
  .description('Insert print tasks from AI JSON response for CSV bulk orders')
  .requiredOption('-o, --order-id <id>', 'Order ID', (val) => parseInt(val, 10))
  .option('-f, --json-file <path>', 'Path to JSON file containing AI response')
  .option('-d, --json-data <data>', 'JSON data as string')
  .parse();

const options = program.opts();

if (!options.jsonFile && !options.jsonData) {
  console.error('Error: Either --json-file or --json-data must be provided');
  process.exit(1);
}

// Run the script
insertTasksFromJSON(options.orderId, options.jsonFile, options.jsonData)
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
